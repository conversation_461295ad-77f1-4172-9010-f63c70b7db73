@import "../../../styles/variables";

.trigger-type-btn-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  .trigger-type-btn {
    border: solid 1px $input-border-color;
    border-radius: 6px;
    background-color: $white-color;
    text-align: left;
    padding: 1rem 5rem 1rem 1.5rem;
    position: relative;

    &.active,
    &:hover {
      background-color: rgba-color($primary-theme-color, 0.05);
    }

    &.active {
      border-color: $primary-theme-color;
      svg-icon.active-check-icon {
        display: block;
      }
    }

    &:disabled {
      pointer-events: none;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .trigger-name {
      color: $primary-text-color;
      font-size: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;

      svg-icon,
      svg-icon svg {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
      }
    }

    .trigger-desc {
      color: rgba-color($primary-text-color, 0.6);
      font-size: 0.875rem;
    }

    svg-icon.active-check-icon {
      display: none;
      position: absolute;
      right: 1.5rem;
      top: 50%;
      transform: translateY(-50%);
      height: 2rem;
      width: 2rem;
      svg {
        height: 2rem;
        width: 2rem;
        path {
          fill: $primary-theme-color;
        }
      }
    }
  }
}

.alert.alert-primary {
  border-radius: 6px;
  border-width: 0;
  background-color: rgba-color($primary-theme-color, 0.1);
}
