.variable-map-legends {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  label {
    position: relative;
    &::before {
      position: absolute;
      content: "";
      width: 0.825rem;
      height: 0.825rem;
      border: solid 1px gray;
      background-color: lightgray;
      border-radius: 50%;
      left: -1.25rem;
      top: 50%;
      transform: translateY(-50%);
    }
    &.mapped::before {
      background-color: #dcfce7;
      border-color: #86efac;
    }
    &.unmapped::before {
      background-color: #fef9c3;
      border-color: #fde047;
    }
  }
}
