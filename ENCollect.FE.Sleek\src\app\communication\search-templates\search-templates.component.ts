import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { SharedModule } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";

@Component({
  selector: "app-search-templates",
  standalone: true,
  imports: [FormsModule, SharedModule],
  templateUrl: "./search-templates.component.html",
  styleUrl: "./search-templates.component.scss",
})
export class SearchTemplatesComponent {
  breadcrumbData = [
    { label: "Communication" },
    { label: "Search Communication Templates" },
  ];
  hasAccess = {
    create: true,
    view: true,
    edit: true,
  };

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 1;
  templates = [
    {
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
    },
    {
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
    },
    {
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
    },
    {
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
    },
    {
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
    },
  ];

  // Filter
  filter = {
    searchTerm: "",
    channel: null,
  };

  isLoading = false;
}
