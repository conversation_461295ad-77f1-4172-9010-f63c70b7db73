{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Directive, ElementRef, EventEmitter, HostBinding, inject, Input, Output, Renderer2 } from \"@angular/core\";\nlet TemplateVarConfigDirective = class TemplateVarConfigDirective {\n  constructor() {\n    this.template = \"\";\n    this.selectVar = new EventEmitter();\n    this.change = new EventEmitter();\n    this.element = inject(ElementRef);\n    this.renderer = inject(Renderer2);\n  }\n  ngOnChanges(changes) {\n    if (changes[\"template\"]) {\n      this.replaceTags();\n    }\n  }\n  replaceTags() {\n    const content = (this.template || \"\")?.replace(/<<(.+?)>>/g, (match, innerText) => {\n      return `<span ${innerText !== \"Var\" ? 'class=\"assigned\"' : \"\"}>${innerText}</span>`;\n    })?.replace(/\\n/g, \"<br>\");\n    this.element.nativeElement.innerHTML = content;\n    this.element.nativeElement.querySelectorAll(\"span\").forEach((span, index) => {\n      this.renderer.listen(span, \"click\", event => {\n        const value = span?.innerHTML;\n        this.selectVar.emit({\n          index,\n          value: value === \"Var\" ? null : value\n        });\n      });\n    });\n  }\n  onUpdateVariable({\n    index,\n    value\n  }) {\n    let matchIndex = -1;\n    const newTemplate = this.template.replace(/<<(.+?)>>/g, (match, p1, offset) => {\n      matchIndex++;\n      if (matchIndex === index) {\n        return `<<${value}>>`;\n      }\n      return match;\n    });\n    this.change.emit(newTemplate);\n  }\n  get hostClass() {\n    return \"form-control template-var-config\";\n  }\n  static {\n    this.propDecorators = {\n      template: [{\n        type: Input\n      }],\n      selectVar: [{\n        type: Output\n      }],\n      change: [{\n        type: Output\n      }],\n      hostClass: [{\n        type: HostBinding,\n        args: [\"class\"]\n      }]\n    };\n  }\n};\nTemplateVarConfigDirective = __decorate([Directive({\n  selector: \"[appTemplateVarConfig]\",\n  exportAs: \"appTemplateVarConfig\",\n  standalone: true\n})], TemplateVarConfigDirective);\nexport { TemplateVarConfigDirective };", "map": {"version": 3, "names": ["Directive", "ElementRef", "EventEmitter", "HostBinding", "inject", "Input", "Output", "Renderer2", "TemplateVarConfigDirective", "constructor", "template", "selectVar", "change", "element", "renderer", "ngOnChanges", "changes", "replaceTags", "content", "replace", "match", "innerText", "nativeElement", "innerHTML", "querySelectorAll", "for<PERSON>ach", "span", "index", "listen", "event", "value", "emit", "onUpdateVariable", "matchIndex", "newTemplate", "p1", "offset", "hostClass", "args", "__decorate", "selector", "exportAs", "standalone"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\shared\\directives\\template-var-config.directive.ts"], "sourcesContent": ["import {\r\n  Directive,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostBinding,\r\n  inject,\r\n  Input,\r\n  OnChanges,\r\n  Output,\r\n  Renderer2,\r\n  SimpleChang<PERSON>,\r\n} from \"@angular/core\";\r\n\r\n@Directive({\r\n  selector: \"[appTemplateVarConfig]\",\r\n  exportAs: \"appTemplateVarConfig\",\r\n  standalone: true,\r\n})\r\nexport class TemplateVarConfigDirective implements OnChanges {\r\n  @Input() template: string = \"\";\r\n  @Output() selectVar: EventEmitter<{ index: number; value: string }> =\r\n    new EventEmitter();\r\n  @Output() change: EventEmitter<string> = new EventEmitter();;\r\n\r\n  element = inject(ElementRef);\r\n  renderer = inject(Renderer2);\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes[\"template\"]) {\r\n      this.replaceTags();\r\n    }\r\n  }\r\n\r\n  replaceTags() {\r\n    const content = (this.template || \"\")\r\n      ?.replace(/<<(.+?)>>/g, (match, innerText) => {\r\n        return `<span ${\r\n          innerText !== \"Var\" ? 'class=\"assigned\"' : \"\"\r\n        }>${innerText}</span>`;\r\n      })\r\n      ?.replace(/\\n/g, \"<br>\");\r\n\r\n    this.element.nativeElement.innerHTML = content;\r\n\r\n    this.element.nativeElement\r\n      .querySelectorAll(\"span\")\r\n      .forEach((span: HTMLElement, index: number) => {\r\n        this.renderer.listen(span, \"click\", (event) => {\r\n          const value = span?.innerHTML;\r\n          this.selectVar.emit({ index, value: value === \"Var\" ? null : value });\r\n        });\r\n      });\r\n  }\r\n\r\n  onUpdateVariable({ index, value }: { index: number; value: string }) {\r\n    let matchIndex = -1;\r\n    const newTemplate = this.template.replace(/<<(.+?)>>/g, (match, p1, offset) => {\r\n      matchIndex++;\r\n      if (matchIndex === index) {\r\n        return `<<${value}>>`;\r\n      }\r\n      return match;\r\n    });\r\n\r\n    this.change.emit(newTemplate);\r\n  }\r\n\r\n  @HostBinding(\"class\") get hostClass() {\r\n    return \"form-control template-var-config\";\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,MAAM,EACNC,KAAK,EAELC,MAAM,EACNC,SAAS,QAEJ,eAAe;AAOf,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAAhCC,YAAA;IACI,KAAAC,QAAQ,GAAW,EAAE;IACpB,KAAAC,SAAS,GACjB,IAAIT,YAAY,EAAE;IACV,KAAAU,MAAM,GAAyB,IAAIV,YAAY,EAAE;IAE3D,KAAAW,OAAO,GAAGT,MAAM,CAACH,UAAU,CAAC;IAC5B,KAAAa,QAAQ,GAAGV,MAAM,CAACG,SAAS,CAAC;EA6C9B;EA3CEQ,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,MAAMC,OAAO,GAAG,CAAC,IAAI,CAACR,QAAQ,IAAI,EAAE,GAChCS,OAAO,CAAC,YAAY,EAAE,CAACC,KAAK,EAAEC,SAAS,KAAI;MAC3C,OAAO,SACLA,SAAS,KAAK,KAAK,GAAG,kBAAkB,GAAG,EAC7C,IAAIA,SAAS,SAAS;IACxB,CAAC,CAAC,EACAF,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAE1B,IAAI,CAACN,OAAO,CAACS,aAAa,CAACC,SAAS,GAAGL,OAAO;IAE9C,IAAI,CAACL,OAAO,CAACS,aAAa,CACvBE,gBAAgB,CAAC,MAAM,CAAC,CACxBC,OAAO,CAAC,CAACC,IAAiB,EAAEC,KAAa,KAAI;MAC5C,IAAI,CAACb,QAAQ,CAACc,MAAM,CAACF,IAAI,EAAE,OAAO,EAAGG,KAAK,IAAI;QAC5C,MAAMC,KAAK,GAAGJ,IAAI,EAAEH,SAAS;QAC7B,IAAI,CAACZ,SAAS,CAACoB,IAAI,CAAC;UAAEJ,KAAK;UAAEG,KAAK,EAAEA,KAAK,KAAK,KAAK,GAAG,IAAI,GAAGA;QAAK,CAAE,CAAC;MACvE,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEAE,gBAAgBA,CAAC;IAAEL,KAAK;IAAEG;EAAK,CAAoC;IACjE,IAAIG,UAAU,GAAG,CAAC,CAAC;IACnB,MAAMC,WAAW,GAAG,IAAI,CAACxB,QAAQ,CAACS,OAAO,CAAC,YAAY,EAAE,CAACC,KAAK,EAAEe,EAAE,EAAEC,MAAM,KAAI;MAC5EH,UAAU,EAAE;MACZ,IAAIA,UAAU,KAAKN,KAAK,EAAE;QACxB,OAAO,KAAKG,KAAK,IAAI;MACvB;MACA,OAAOV,KAAK;IACd,CAAC,CAAC;IAEF,IAAI,CAACR,MAAM,CAACmB,IAAI,CAACG,WAAW,CAAC;EAC/B;MAE0BG,SAASA,CAAA;IACjC,OAAO,kCAAkC;EAC3C;;;;cAlDChC;MAAK;;cACLC;MAAM;;cAENA;MAAM;;cA6CNH,WAAW;QAAAmC,IAAA,GAAC,OAAO;MAAA;;;;AAjDT9B,0BAA0B,GAAA+B,UAAA,EALtCvC,SAAS,CAAC;EACTwC,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE;CACb,CAAC,C,EACWlC,0BAA0B,CAoDtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}