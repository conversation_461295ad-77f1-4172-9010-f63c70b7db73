{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./search-templates.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./search-templates.component.scss?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { FormsModule } from \"@angular/forms\";\nimport { SharedModule } from \"src/app/shared\";\nlet SearchTemplatesComponent = class SearchTemplatesComponent {\n  constructor() {\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Search Communication Templates\"\n    }];\n    this.hasAccess = {\n      create: true,\n      view: true,\n      edit: true\n    };\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalItems = 1;\n    this.templates = [{\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\", \"ta\"]\n    }, {\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\", \"ta\"]\n    }, {\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\", \"hi\", \"ta\"]\n    }, {\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\", \"hi\", \"ta\"]\n    }, {\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\", \"hi\", \"ta\"]\n    }];\n    // Filter\n    this.filter = {\n      searchTerm: \"\",\n      channel: null\n    };\n    this.isLoading = false;\n  }\n};\nSearchTemplatesComponent = __decorate([Component({\n  selector: \"app-search-templates\",\n  standalone: true,\n  imports: [FormsModule, SharedModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SearchTemplatesComponent);\nexport { SearchTemplatesComponent };", "map": {"version": 3, "names": ["Component", "FormsModule", "SharedModule", "SearchTemplatesComponent", "constructor", "breadcrumbData", "label", "hasAccess", "create", "view", "edit", "currentPage", "itemsPerPage", "totalItems", "templates", "templateName", "description", "channel", "languages", "filter", "searchTerm", "isLoading", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\search-templates\\search-templates.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\";\r\nimport { FormsModule } from \"@angular/forms\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\n\r\n@Component({\r\n  selector: \"app-search-templates\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule],\r\n  templateUrl: \"./search-templates.component.html\",\r\n  styleUrl: \"./search-templates.component.scss\",\r\n})\r\nexport class SearchTemplatesComponent {\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Search Communication Templates\" },\r\n  ];\r\n  hasAccess = {\r\n    create: true,\r\n    view: true,\r\n    edit: true,\r\n  };\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n  totalItems = 1;\r\n  templates = [\r\n    {\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"ta\"],\r\n    },\r\n    {\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\",\"ta\"],\r\n    },\r\n    {\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"hi\", \"ta\"],\r\n    },\r\n    {\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"hi\", \"ta\"],\r\n    },\r\n    {\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"hi\", \"ta\"],\r\n    },\r\n  ];\r\n\r\n  // Filter\r\n  filter = {\r\n    searchTerm: \"\",\r\n    channel: null,\r\n  };\r\n\r\n  isLoading = false;\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAUtC,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC,YAAA;IACL,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAAgC,CAAE,CAC5C;IACD,KAAAC,SAAS,GAAG;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;KACP;IAED;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,CACV;MACEC,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI;KACvB,EACD;MACEH,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAC,IAAI;KACtB,EACD;MACEH,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;KAC7B,EACD;MACEH,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;KAC7B,EACD;MACEH,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;KAC7B,CACF;IAED;IACA,KAAAC,MAAM,GAAG;MACPC,UAAU,EAAE,EAAE;MACdH,OAAO,EAAE;KACV;IAED,KAAAI,SAAS,GAAG,KAAK;EACnB;CAAC;AAvDYlB,wBAAwB,GAAAmB,UAAA,EAPpCtB,SAAS,CAAC;EACTuB,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxB,WAAW,EAAEC,YAAY,CAAC;EACpCwB,QAAA,EAAAC,oBAAgD;;CAEjD,CAAC,C,EACWxB,wBAAwB,CAuDpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}