<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <h2 class="title">Create Communication Template</h2>
  <div class="enc-card" [formGroup]="createForm">
    <div class="card-header">
      <div class="d-flex align-items-center">
        <h3>Create Communication Template</h3>
        <button
          tooltip="Design templates for different communication channels with variable
          support and multilingual capabilities"
          class="info-icon ms-3"
          type="button"
        >
          <svg-icon src="assets/new/svgs/instruction-info.svg"></svg-icon>
        </button>
      </div>
    </div>
    <div class="card-content">
      <div class="row">
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Channel Type</label>
            <div class="form-radio-group">
              <label>
                <input
                  type="radio"
                  id="channelTypeEmail"
                  [value]="'email'"
                  formControlName="channelType"
                />
                Email
              </label>
              <label>
                <input
                  type="radio"
                  id="channelTypeSMS"
                  [value]="'sms'"
                  formControlName="channelType"
                />
                SMS
              </label>
              <label>
                <input
                  type="radio"
                  id="channelTypeLetter"
                  [value]="'letter'"
                  formControlName="channelType"
                />
                Letter
              </label>
            </div>
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Name</label>
            <input
              type="text"
              class="form-control"
              placeholder="Enter Template Name"
              id="templateName"
              formControlName="templateName"
            />
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Language</label>
            <div class="d-flex align-items-center justify-content-between">
              <div class="form-button-group" btnRadioGroup formControlName="activeLanguage">
                @for (lang of fValue?.languages; track lang.languageCode) {
                  <button
                    [btnRadio]="lang?.languageCode"
                    id="template-language-{{ lang?.languageCode }}"
                  >
                    {{ lang?.languageName }}
                  </button>
                }
              </div>
              <button class="btn btn-outline-primary" id="addLanguageBtn" (click)="openAddLangModal(addLangModal)">
                <svg-icon
                  src="assets/new/svgs/language.svg"
                  class="me-2"
                ></svg-icon>
                <span>Add Language</span>
              </button>
            </div>
          </div>
        </div>

        @if (createForm?.get('channelType')?.value === 'letter') {
          <div class="col-md-6">
            <div class="form-control-group">
              <label class="form-label required">Upload Header</label>
              <input
                type="file"
                class="form-control"
                accept=".jpg,.jpeg,.png,.pdf"
                (change)="onHeaderUpload($event)"
              />
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-control-group">
              <label class="form-label required">Upload Footer</label>
              <input
                type="file"
                class="form-control"
                accept=".jpg,.jpeg,.png,.pdf"
                (change)="onFooterUpload($event)"
              />
            </div>
          </div>
        }

        @for (lang of fValue?.languages; let i = $index; track lang.languageCode) {
          <ng-container [formArrayName]="'languages'">
            <ng-container [formGroupName]="i">
              @if (fValue?.activeLanguage === fValue?.languages?.[i]?.languageCode) {
                @if (fValue?.channelType === 'email') {
                  <div class="col-md-12">
                    <div class="form-control-group">
                      <label class="form-label required">Subject Line</label>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Enter Email Subject"
                        id="emailSubject"
                        formControlName="emailSubject"
                      />
                    </div>
                  </div>
                }

                <div class="col-md-12">
                  <div class="form-control-group">
                    <label class="form-label required">Template Body</label>
                    <textarea
                      class="form-control"
                      placeholder="Enter Approved Template Content"
                      id="templateBody"
                      rows="7"
                      formControlName="templateBody"
                    ></textarea>
                  </div>
                </div>

                @if (fValue?.languages?.[i]?.templateBody) {
                  <div class="col-md-12">
                    <div class="form-control-group">
                      <div class="d-flex align-items-center justify-content-between">
                        <label class="form-label required">Template Preview</label>
                        <div class="variable-map-legends">
                          <label class="unmapped">Unmapped Variable</label>
                          <label class="mapped">Mapped Variable</label>
                        </div>
                      </div>
                      <div appTemplateVarConfig #templateVarConfig="appTemplateVarConfig" [template]="fValue?.languages?.[i]?.templateBody" (selectVar)="openMapVariableModal($event, mapVariableModal)" (change)="updateTemplateValue($event, i)"></div>
                    </div>
                  </div>
                }
              }
            </ng-container>
          </ng-container>
        }

        <div class="col-md-12">
          <!-- {{ fValue | json }} -->
        </div>
      </div>
    </div>
    <div class="card-footer">
      <button class="btn btn-secondary mw-150px me-4">Create Template</button>
      <button class="btn btn-outline-primary mw-150px">Cancel</button>
    </div>
  </div>
</div>

<ng-template #mapVariableModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Map Variable to Database Field</h4>
    <button
      type="button"
      class="close"
      aria-label="Close"
      (click)="mapVarModalRef?.hide()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Datebase Field</label>
      <ng-select
        class="form-ng-select"
        [items]="variables"
        bindLabel="name"
        bindValue="id"
        placeholder="Select Database Field"
        [(ngModel)]="selectedVariable.value"
        [clearable]="false"
        [searchable]="true"
      >
        <!-- [appendTo]="'body'" -->
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button
      type="button"
      class="btn btn-success mw-150px"
      [disabled]="!selectedVariable?.value"
      (click)="assignVariable()"
    >
      Map
    </button>
  </div>
</ng-template>

<ng-template #addLangModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Add New Language</h4>
    <button
      type="button"
      class="close"
      aria-label="Close"
      (click)="addLangModalRef?.hide()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Language Code</label>
      <ng-select
        class="form-ng-select"
        [items]="allLanguages"
        bindLabel="name"
        bindValue="code"
        placeholder="Select Language"
        [(ngModel)]="selectedLanguage"
        [clearable]="false"
        [searchable]="true"
      >
        <!-- [appendTo]="'body'" -->
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button
      type="button"
      class="btn btn-success mw-150px"
      [disabled]="!selectedLanguage"
      (click)="addLanguage()"
    >
      Add Language
    </button>
  </div>
</ng-template>
