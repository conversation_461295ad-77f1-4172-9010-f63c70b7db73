@import "./variables";

$primary: $primary-theme-color;
$btn-primary-bg: $primary;
$secondary: $secondary-theme-color;
$btn-secondary-bg: $secondary;
$dark: $primary-text-color;
$btn-secondary-bg: $dark;

@import "./mixins";
@import "node_modules/bootstrap/scss/bootstrap";

* {
  font-family: $font-family;
  font-optical-sizing: auto;
  font-style: normal;

  &::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  &::-webkit-scrollbar-track {
    background: #f0f0f0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 6px;
    border: 3px solid #f0f0f0;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  &.scrollable-div {
    scrollbar-width: thin;
    scrollbar-color: #888 #f0f0f0;
  }
}

html {
  font-size: 14px;
}

.toast-container .ngx-toastr {
  width: 37.5rem;
  border-radius: 10px;
  padding: 1.125rem 3.75rem 1.125rem 6.25rem;
  position: relative;
  min-height: 5.75rem;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: space-evenly;
  margin-bottom: 0.5rem;
  &::before {
    content: "";
    position: absolute;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    top: 50%;
    left: 1.25rem;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 1.5rem;
  }
  .toast-close-button {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 3.75rem;
    background-image: url(../assets/new/svgs/toast-close.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 1.5rem;
    span {
      display: none;
    }
  }
  .toast-title {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: -0.1px;
  }
  .toast-message {
    font-size: 1.125rem;
    letter-spacing: -0.09px;
  }
  &.toast-success {
    background: transparent linear-gradient(270deg, #1d1936 85%, #16965b 100%)
      0% 0% no-repeat padding-box;
    .toast-title {
      color: #20c820;
    }
    &::before {
      background-color: #20c820;
      background-image: url(../assets/new/svgs/toast-success.svg);
    }
  }
  &.toast-warning {
    background: transparent linear-gradient(270deg, #1d1936 85%, #a26817 100%)
      0% 0% no-repeat padding-box;
    .toast-title {
      color: #f69b1f;
    }
    &::before {
      background-color: #f69b1f;
      background-image: url(../assets/new/svgs/toast-warning.svg);
    }
  }
  &.toast-info {
    background: transparent linear-gradient(270deg, #1d1936 85%, #1154ae 100%)
      0% 0% no-repeat padding-box;
    .toast-title {
      color: #0f98d1;
    }
    &::before {
      background-color: #0f98d1;
      background-image: url(../assets/new/svgs/toast-info.svg);
    }
  }
  &.toast-error {
    background: transparent linear-gradient(270deg, #1d1936 85%, #af1343 100%)
      0% 0% no-repeat padding-box;
    .toast-title {
      color: #ef1752;
    }
    &::before {
      background-color: #ef1752;
      background-image: url(../assets/new/svgs/toast-error.svg);
    }
  }
}

.bs-datepicker {
  .bs-datepicker-head {
    background-color: $primary-theme-color;
    color: white;
  }
  .bs-datepicker-body table td span {
    &.selected {
      background-color: $primary-theme-color;
    }
    &:hover {
      background-color: $bg-color;
    }
  }
}

// Common SCSS
.primary-text-color {
  color: $primary-text-color;
}

.secondary-text-color {
  color: $secondary-text-color;
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

// Font Size

.font-size-8px {
  font-size: 0.5rem;
}

.font-size-12px {
  font-size: 0.75rem;
}

.font-size-16px {
  font-size: 1rem;
}

.font-size-20px {
  font-size: 1.25rem;
}

.font-size-24px {
  font-size: 1.5rem;
}

.font-size-28px {
  font-size: 1.75rem;
}

.font-size-32px {
  font-size: 2rem;
}

.font-size-36px {
  font-size: 2.25rem;
}

.font-size-40px {
  font-size: 2.5rem;
}

.font-size-44px {
  font-size: 2.75rem;
}

.font-size-48px {
  font-size: 3rem;
}

.mw-50px {
  min-width: 3.125rem;
}
.mw-100px {
  min-width: 6.25rem;
}
.mw-150px {
  min-width: 9.375rem;
}
.mw-200px {
  min-width: 12.5rem;
}
.mw-250px {
  min-width: 15.625rem;
}
.mw-300px {
  min-width: 18.75rem;
}
.w-100px {
  width: 6.25rem;
}

// Button Overwrites
.btn {
  font-size: 1.125rem;
  height: 3rem;
  font-weight: 500;
}
a.btn {
  line-height: 2.25rem;
}

.btn.icon-btn {
  width: 3rem;
  img {
    height: 1.125rem;
  }
}

@mixin btn-outline-variant($color) {
  color: $color;
  background-color: transparent;
  border: 1px solid $color;
  &:hover,
  &:focus {
    background-color: rgba($color, 0.05);
    color: $color;
  }
  &:active,
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($color, 0.25);
  }
  &.btn:active,
  &.btn.active {
    color: $color;
    background-color: rgba($color, 0.1);
  }
}

.btn-outline-primary {
  @include btn-outline-variant($primary);
}

.btn-outline-secondary {
  @include btn-outline-variant($secondary);
}

.btn-outline-info {
  @include btn-outline-variant($info);
}

.btn-outline-success {
  @include btn-outline-variant($success);
}

.btn-outline-danger {
  @include btn-outline-variant($danger);
}

.btn-outline-warning {
  @include btn-outline-variant($warning);
}

.btn-outline-dark {
  @include btn-outline-variant($dark);
}

.btn-outline-light {
  @include btn-outline-variant($light);
  color: $dark;
  &:hover,
  &:focus {
    color: #fff;
  }
}

.btn-outline-link {
  color: $primary;
  background-color: transparent;
  border: none;
  text-decoration: underline;
  &:hover,
  &:focus {
    color: darken($primary, 10%);
    text-decoration: none;
  }
  &:active,
  &:focus {
    box-shadow: none;
    text-decoration: none;
  }
}

@mixin btn-shade-variant($color) {
  color: $color;
  background-color: rgba($color, 0.05);
  border: none;
  &:hover,
  &:focus {
    background-color: rgba($color, 0.1);
    color: $color;
  }
  &:active,
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($color, 0.5);
  }
  &:disabled {
    opacity: 0.3;
  }
}

.btn-shade-primary {
  @include btn-shade-variant($primary);
}

.btn-shade-secondary {
  @include btn-shade-variant($secondary);
}

.btn-shade-info {
  @include btn-shade-variant($info);
}

.btn-shade-success {
  @include btn-shade-variant($success);
}

.btn-shade-danger {
  @include btn-shade-variant($danger);
}

.btn-shade-warning {
  @include btn-shade-variant($warning);
}

.btn-shade-dark {
  @include btn-shade-variant($dark);
}

.btn-shade-light {
  @include btn-shade-variant($light);
  color: $dark;
  &:hover,
  &:focus {
    color: #fff;
  }
}

.btn-shade-link {
  color: $primary;
  background-color: transparent;
  border: none;
  text-decoration: underline;
  &:hover,
  &:focus {
    color: darken($primary, 10%);
    text-decoration: none;
  }
  &:active,
  &:focus {
    box-shadow: none;
    text-decoration: none;
  }
}

// Form Control Group SCSS
.form-control-group {
  margin-bottom: 1.5rem;
  position: relative;
  .form-label {
    color: $secondary-text-color;
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.25rem;
    margin-bottom: 0.375rem;
    &.required::after,
    span.required::after {
      content: "*";
      color: #c82350;
    }
    & + .form-radio-group:not(.with-box),
    & + .form-check-group:not(.with-box),
    & + .form-toggle-group:not(.with-box) {
      margin-top: 0.3rem;
    }
  }
  input.form-control {
    color: $primary-text-color;
    font-size: 1rem;
    line-height: 1.25rem;
    padding: 0.875rem 1rem;
    border-radius: 6px;
    border-color: $input-border-color;
    &[type="file"] {
      padding: 0.375rem 0.75rem;
      line-height: 2.25rem;
    }
    &[bsdatepicker],
    &[bsdaterangepicker] {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14.286' height='15' viewBox='0 0 14.286 15'%3E%3Cg id='Group_357' data-name='Group 357' transform='translate(-4 -3)'%3E%3Crect id='Rectangle_229' data-name='Rectangle 229' width='1.429' height='2.143' rx='0.714' transform='translate(6.5 3)' fill='%233f3b53'/%3E%3Crect id='Rectangle_230' data-name='Rectangle 230' width='1.429' height='2.143' rx='0.714' transform='translate(14.357 3)' fill='%233f3b53'/%3E%3Cpath id='Path_158' data-name='Path 158' d='M4,18v8.214a1.429,1.429,0,0,0,1.429,1.429H16.857a1.429,1.429,0,0,0,1.429-1.429V18Zm4.286,7.143a.714.714,0,0,1-.714.714H6.857a.714.714,0,0,1-.714-.714v-.714a.714.714,0,0,1,.714-.714h.714a.714.714,0,0,1,.714.714Zm0-3.929a.714.714,0,0,1-.714.714H6.857a.714.714,0,0,1-.714-.714V20.5a.714.714,0,0,1,.714-.714h.714a.714.714,0,0,1,.714.714Zm3.929,3.929a.714.714,0,0,1-.714.714h-.714a.714.714,0,0,1-.714-.714v-.714a.714.714,0,0,1,.714-.714H11.5a.714.714,0,0,1,.714.714Zm0-3.929a.714.714,0,0,1-.714.714h-.714a.714.714,0,0,1-.714-.714V20.5a.714.714,0,0,1,.714-.714H11.5a.714.714,0,0,1,.714.714Zm3.929,3.929a.714.714,0,0,1-.714.714h-.714A.714.714,0,0,1,14,25.143v-.714a.714.714,0,0,1,.714-.714h.714a.714.714,0,0,1,.714.714Zm0-3.929a.714.714,0,0,1-.714.714h-.714A.714.714,0,0,1,14,21.214V20.5a.714.714,0,0,1,.714-.714h.714a.714.714,0,0,1,.714.714Z' transform='translate(0 -9.643)' fill='%233f3b53'/%3E%3Cpath id='Path_159' data-name='Path 159' d='M18.286,9.571V7.429A1.429,1.429,0,0,0,16.857,6H16.5v.357a1.429,1.429,0,1,1-2.857,0V6h-5v.357a1.429,1.429,0,0,1-2.857,0V6H5.429A1.429,1.429,0,0,0,4,7.429V9.571Z' transform='translate(0 -1.929)' fill='%233f3b53'/%3E%3C/g%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: calc(100% - 1.25rem) center;
      background-size: 1.25rem;
    }
    &::placeholder {
      color: rgba-color($secondary-text-color, 0.6);
    }
    &:not([type="file"]):read-only {
      background-color: var(--bs-secondary-bg);
    }
    &.ng-invalid.ng-dirty,
    &.ng-invalid.ng-touched {
      border-color: $red-color;
    }
  }
  textarea.form-control {
    color: $primary-text-color;
    font-size: 1rem;
    line-height: 1.25rem;
    padding: 0.875rem 1rem;
    border-radius: 6px;
  }
  select.form-select {
    color: $primary-text-color;
    font-size: 1rem;
    line-height: 1.25rem;
    padding: 0.875rem 1rem;
    border-radius: 6px;
    border-color: $input-border-color;
  }
  .form-control-with-icon {
    position: relative;
    img.icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      width: 1.25rem;
    }
    &.end {
      input.form-control,
      select.form-select {
        padding-right: 2.5rem;
      }
      img.icon {
        right: 10px;
      }
      select.form-select + img.icon {
        right: 40px;
      }
    }

    &.start {
      input.form-control,
      select.form-select {
        padding-left: 2.5rem;
      }
      img.icon {
        left: 10px;
      }
      select.form-select + img.icon {
        left: 40px;
      }
    }
  }
  ng-select.form-ng-select {
    // For Both Single & Multi Select
    .ng-select-container {
      &:hover {
        box-shadow: none;
      }
      .ng-arrow-wrapper,
      .ng-clear-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        height: 1.25rem;
        width: 1.25rem;
        .ng-arrow {
          border: none;
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: center;
          width: 16px;
          height: 12px;
          transition: transform 0.3s ease;
        }
        .ng-clear {
          font-size: 1.75rem;
          line-height: 2rem;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    &.ng-select-opened > .ng-select-container .ng-arrow {
      top: auto;
      transform: rotate(180deg);
    }

    // For Multi Select
    &.ng-select-multiple {
      .ng-select-container {
        border: 1px solid $input-border-color;
        border-radius: 6px;
        // color: $primary-text-color;
        // font-size: 1rem;
        // line-height: 1.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        .ng-value-container {
          padding: 0;
          display: flex;
          gap: 5px;
          flex-wrap: wrap;
          .ng-placeholder {
            padding: 0;
            top: inherit;
          }
          .ng-input {
            padding: 0;
          }
          .ng-value {
            margin: 0;
            background-color: $bg-color;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            height: 1.25rem;
            .ng-value-icon {
              border-left: 1px solid $border-color;
              border-right: none !important;
              font-size: 1.25rem;
              height: 1.25rem;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: normal;
              font-weight: 500;
              &:hover {
                background-color: #e1b8b8;
                color: $red-color;
              }
            }
          }
        }
      }

      &.ng-select-focused:not(.ng-select-opened) > .ng-select-container {
        border-color: $primary-theme-color;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
          0 0 0 3px rgba-color($primary-theme-color, 0.1);
      }
    }

    // For Single Select
    &.ng-select-single {
      .ng-select-container {
        border: 1px solid $border-color;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.85rem 1rem;
        height: 100%;
        & > * {
          padding: 0;
        }
        .ng-input {
          transform: translateY(-50%);
          top: 50%;
          padding-left: 1rem;
        }
      }
    }
  }
  .form-radio-group,
  .form-check-group {
    display: flex;
    align-items: center;
    gap: 1rem 2rem;
    flex-wrap: wrap;
    &.with-box {
      background-color: map-get($primary-theme, "25");
      border-radius: 15px;
      padding: 1rem 1.25rem;
    }
    &.vertical {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    & > label {
      cursor: pointer;
      margin-bottom: 0;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      line-height: 0;
      font-weight: 500;
      &:has(input:disabled) {
        cursor: not-allowed;
        color: $gray-color;
      }
      input[type="radio"],
      input[type="checkbox"] {
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid $primary-theme-color;
        background: #ffffff;
        box-shadow: 0px 2px 9px #00000030;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        &:checked,
        &:indeterminate {
          background-color: $white-color;
          border-color: $white-color;
          &::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 11px;
            height: 11px;
            background-color: $primary-theme-color;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: none;
          }
        }
        &:hover {
          box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.2);
        }
        &:disabled {
          border-color: $gray-color;
          cursor: not-allowed;
          &:checked::before,
          &:indeterminate::before {
            background-color: $gray-color;
          }
        }
      }
    }
  }
  .form-radio-group > label input[type="radio"]:indeterminate {
    border-color: $primary-theme-color;
    &::before {
      background-color: $white-color;
    }
  }
  .form-check-group > label input[type="checkbox"] {
    border-radius: 4px;
    &:checked,
    &:indeterminate {
      background-color: $primary-theme-color;
      &::before {
        width: 15px;
        height: 15px;
        background-color: transparent;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
      }
      &:disabled {
        background-color: $gray-color;
      }
    }
    &:checked::before {
      background-image: url("data:image/svg+xml,%3Csvg%20height%3D%22417pt%22%20viewBox%3D%220%20-46%20417.81333%20417%22%20width%3D%22417pt%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22m159.988281%20318.582031c-3.988281%204.011719-9.429687%206.25-15.082031%206.25s-11.09375-2.238281-15.082031-6.25l-120.449219-120.46875c-12.5-12.5-12.5-32.769531%200-45.246093l15.082031-15.085938c12.503907-12.5%2032.75-12.5%2045.25%200l75.199219%2075.203125%20203.199219-203.203125c12.503906-12.5%2032.769531-12.5%2045.25%200l15.082031%2015.085938c12.5%2012.5%2012.5%2032.765624%200%2045.246093zm0%200%22%2F%3E%3C%2Fsvg%3E");
    }
    &:indeterminate::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M5 12h14'/%3E%3C/svg%3E");
    }
  }
  .form-toggle-group {
    & > label {
      cursor: pointer;
      margin-bottom: 0;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      line-height: 0;
      font-weight: 500;
      &:has(input:disabled) {
        cursor: not-allowed;
        color: $gray-color;
      }
      input[type="checkbox"] {
        appearance: none;
        position: relative;
        cursor: pointer;
        height: 20px;
        width: 40px;
        border-radius: 20px;
        background-color: $white-color;
        &::before {
          content: "";
          position: absolute;
          height: 20px;
          width: 40px;
          background: $white-color;
          background-color: rgba-color($primary-theme-color, 0.3);
          border-radius: 20px;
          transition: all 0.4s ease-in-out;
        }
        &:checked::before {
          background: inherit;
          background-color: $primary-theme-color;
          opacity: 0.8;
        }
        &::after {
          content: "";
          position: absolute;
          height: 14px;
          width: 14px;
          background-color: $primary-theme-color;
          border-radius: 50%;
          top: 50%;
          left: 3px;
          right: auto;
          transform: translateY(-50%);
          transition: all 0.4s ease-in-out;
        }
        &:checked::after {
          background-color: $white-color;
          right: 3px;
          left: auto;
        }
      }
    }
    &.with-box {
      background-color: map-get($primary-theme, "25");
      border-radius: 15px;
      padding: 1rem 1.25rem;
    }
  }
  .form-button-group {
    border-radius: 6px;
    padding: 0.25rem;
    background: rgba-color($primary-theme-color, 0.05);
    button {
      border: solid 1px transparent;
      border-radius: 6px;
      background-color: transparent;
      font-size: 1rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      &.active {
        border-color: $border-color;
        background-color: $white-color;
      }
    }
  }
  .form-control.template-var-config {
    color: #3f3b53;
    font-size: 1rem;
    padding: 0.875rem 1rem;
    border-radius: 6px;
    span {
      background-color: #fef9c3;
      color: #854d0e;
      border: solid 1px #fde047;
      padding: 0.1rem 0.5rem;
      border-radius: 5px;
      font-size: 0.85rem;
      cursor: pointer;
      &.assigned {
        background-color: #dcfce7;
        color: #16a34a;
        border: solid 1px #86efac;
      }
    }
  }
  .form-error {
    color: $red-color;
    font-size: 0.8rem;
    font-weight: 500;
    position: absolute;
    margin-top: 0.1rem;
    letter-spacing: 0.2px;
  }
}

.ng-dropdown-panel.form-ng-select {
  border-radius: 6px !important;
  border-color: $input-border-color;
  overflow: hidden;
  .ng-dropdown-panel-items {
    .ng-option {
      border-bottom: 1px solid #d8d3e8;
      &:hover,
      &.ng-option-selected,
      &.ng-option-marked {
        background-color: $bg-color;
      }
      &.ng-option-disabled {
        color: $gray-color;
      }
    }
  }
  &.ng-select-bottom {
    margin-top: 5px;
  }
  &.ng-select-multiple .ng-dropdown-panel-items {
    .ng-option {
      position: relative;
      padding-left: 36px;
      &::before {
        content: "";
        position: absolute;
        top: calc(50% - 8px);
        left: 10px;
        height: 16px;
        width: 16px;
        border-radius: 4px;
        background-color: $white-color;
        border: solid 1px $primary-theme-color;
        box-shadow: 0px 2px 9px #00000030;
      }
      &.ng-option-selected {
        &::before {
          background-color: $primary-theme-color;
          border-color: $white-color;
        }
        &::after {
          content: "";
          position: absolute;
          top: 50%;
          left: 18px;
          transform: translate(-50%, -50%);
          width: 11px;
          height: 11px;
          border-radius: 50%;
          background-color: transparent;
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          background-image: url("data:image/svg+xml,%3Csvg%20height%3D%22417pt%22%20viewBox%3D%220%20-46%20417.81333%20417%22%20width%3D%22417pt%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22m159.988281%20318.582031c-3.988281%204.011719-9.429687%206.25-15.082031%206.25s-11.09375-2.238281-15.082031-6.25l-120.449219-120.46875c-12.5-12.5-12.5-32.769531%200-45.246093l15.082031-15.085938c12.503907-12.5%2032.75-12.5%2045.25%200l75.199219%2075.203125%20203.199219-203.203125c12.503906-12.5%2032.769531-12.5%2045.25%200l15.082031%2015.085938c12.5%2012.5%2012.5%2032.765624%200%2045.246093zm0%200%22%2F%3E%3C%2Fsvg%3E");
        }
      }
      &.ng-option-disabled {
        &::before {
          border-color: $gray-color;
        }
        &.ng-option-selected::before {
          border-color: $white-color;
          background-color: $gray-color;
        }
      }
    }
  }
}

.inner-layout-container {
  height: calc(100vh - 5rem);
  scroll-behavior: smooth;
  h2.title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $primary-text-color;
    margin-bottom: 1.5rem;
    // display: flex;
    // align-items: center;
    // padding-left: 3rem;
    // position: relative;
    // &::before {
    //   content: "";
    //   background-image: url(../assets/new/svgs/title-back-arrow.svg);
    //   width: 2.2rem;
    //   height: 2.2rem;
    //   border: 1px solid $primary-text-color;
    //   border-radius: 6px;
    //   position: absolute;
    //   background-repeat: no-repeat;
    //   background-position: center;
    //   background-size: 1.2rem;
    //   left: 0;
    // }
  }
  &:not(:has(.inner-container)) {
    padding: 1rem 1.25rem;
    overflow: auto;
  }
  &:has(.inner-container) {
    display: flex;
    position: relative;
    overflow-x: hidden;
  }
  .inner-container {
    flex: 1;
    padding: 1rem 1.25rem;
    overflow: auto;
  }
  .side-container {
    border-radius: 30px 0 0 30px;
    background-color: $white-color;
    box-shadow: 3px 0px 10px rgba(0, 0, 0, 0.4);
    flex-basis: 20rem;
    display: flex;
    flex-direction: column;
    .side-header {
      padding: 1.875rem 1.625rem;
      h3 {
        font-size: 1.5rem;
        font-weight: 500;
        margin-bottom: 0.75rem;
      }
    }
    .side-content {
      flex: 1;
      overflow: auto;
      padding: 0 1.625rem 1.875rem 1.625rem;
    }
  }

  .enc-offcanvas-container {
    flex-basis: auto;
    display: flex;
    flex-direction: column;
    transition: width 0.6s ease-in-out, opacity 0.3s ease-in-out;

    &.hide {
      width: 0;
      opacity: 0;
    }

    &.show:not(.over) {
      width: 22rem;
      opacity: 1;
    }

    &.hide.over {
      opacity: 1;
      .enc-offcanvas-content {
        transform: translateX(0);
      }
    }

    &.show.over {
      width: 0;
      opacity: 1;
      .enc-offcanvas-content {
        transform: translateX(-100%);
      }
    }

    .enc-offcanvas-content {
      width: 22rem;
      display: flex;
      flex: 1;
      overflow: auto;
      flex-direction: column;
      transition: transform 0.3s ease-in-out;

      background-color: $white-color;
      border-radius: 0 0 0 20px;
      box-shadow: 3px 0px 10px rgba(0, 0, 0, 0.4);
    }

    .enc-offcanvas-backdrop {
      background-color: rgba-color($black-color, 0.3);
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      transition: opacity 0.3s ease-in-out; // , z-index 0.3s ease-in-out
    }

    &.hide .enc-offcanvas-backdrop {
      opacity: 0;
      z-index: -1;
    }

    &.show .enc-offcanvas-backdrop {
      opacity: 1;
      z-index: 0;
    }
  }
}

.enc-card {
  border-radius: 20px;
  overflow: hidden;
  background-color: $white-color;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.15rem 1.75rem;
    border-bottom: 2px solid $border-color;
    background-color: $white-color;
    h3 {
      font-size: 1.15rem;
      font-weight: 600;
      margin: 0;
    }
    h5 {
      font-size: 0.9rem;
      font-weight: 600;
      margin-top: 0.35rem;
      margin-bottom: 0;
      color: rgba-color($primary-text-color, 0.6);
    }

    .info-icon {
      padding: 0;
      border: none;
      background: transparent;
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      svg-icon svg {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
  .card-content {
    background-color: $white-color;
    padding: 1.5rem 1.75rem;
  }
  & > .card-content.overflow-auto.p-0:has(> .enc-table),
  & > .card-content.overflow-auto.p-0:has(> .enc-table-1) {
    max-height: calc(100vh - 20rem);
  }
  .card-footer {
    background-color: $white-color;
    padding: 1.25rem 1.75rem;
    border-top: 2px solid $border-color;
  }
}

.enc-tabs {
  display: block;
  border: none;
  box-shadow: none;
  background-color: white;
  border-radius: 4px;
  position: relative;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .nav-tabs {
    display: flex;
    border-bottom: none;
    position: relative;
    gap: 0 1.75rem;
    padding: 0 1.75rem;
    border-bottom: 2px solid $border-color;
    flex-wrap: nowrap;
  }

  &.scrollable {
    .scroll-nav-container {
      border-bottom: 2px solid $border-color;
      width: 100%;
      display: flex;
      .nav-tabs {
        display: flex;
        overflow-x: auto;
        scroll-behavior: smooth;
        scrollbar-width: none;
        border-bottom: none;
        flex: 1;
        &::-webkit-scrollbar {
          display: none;
        }
      }

      .scroll-btn {
        &.left-btn {
          left: 0;
          box-shadow: 1px 0px 10px rgba(53, 40, 117, 0.125);
          background-image: url(../assets/new/svgs/pagination-prev.svg);
        }

        &.right-btn {
          right: 0;
          box-shadow: -1px 0px 10px rgba(53, 40, 117, 0.125);
          background-image: url(../assets/new/svgs/pagination-next.svg);
        }

        &.left-btn,
        &.right-btn {
          position: sticky;
          // z-index: 1;
          border: none;
          padding: 1.5rem;
          background-color: $white-color;
          background-repeat: no-repeat;
          background-position: center;
          background-size: 0.75rem;
        }
      }
    }
  }

  .nav-item {
    margin-bottom: 0;

    .nav-link {
      text-wrap: nowrap;
      color: rgba(0, 0, 0, 0.6);
      font-size: 1rem;
      padding: 1.5rem 0;
      border: none;
      font-weight: 500;
      position: relative;
      transition: color 0.3s ease, font-weight 0.3s ease;
      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 50%;
        width: 0;
        height: 4px;
        background-color: $primary-theme-color;
        transition: width 0.3s ease;
        transform: translateX(-50%);
      }
    }

    .nav-link.active {
      color: $primary-theme-color;
      font-weight: 600;
    }
  }

  .nav-link.active::after {
    width: 100%;
  }

  .nav-link:hover,
  .nav-link:focus {
    color: $primary-theme-color;
  }

  .tab-content {
    margin-top: 0;
    padding: 1.5rem 1.75rem;
    .tab-pane {
      opacity: 0;
      transition: opacity 0.5s ease;
      &.active {
        opacity: 1;
      }
    }
  }

  &.no-padding .tab-content {
    padding: 0;
  }
}

.enc-tabs-1 {
  .nav-tabs {
    border-bottom: 3px solid var(--bs-secondary);
    gap: 0.25rem;

    .nav-item .nav-link {
      border-bottom: 0;
      margin-bottom: 0;
      border-radius: 20px 20px 0px 0px;
      font-size: 1.25rem;
      padding: 1.25rem 4rem;
      background-color: rgba-color($gray-color, 0.2);
      font-weight: 600;
      color: $black-color;
      &:focus-visible {
        box-shadow: 0 0 0 0.1rem var(--bs-secondary);
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      color: var(--bs-black);
      background-color: var(--bs-secondary);
      border-color: transparent;
    }
  }
}

.table.enc-table {
  margin: 0;
  thead {
    position: sticky;
    top: 0;
    z-index: 2;
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.0588235294);
  }
  thead th,
  tfoot th,
  tbody td,
  tbody th {
    padding: 1rem 1.25rem;
  }
  thead th,
  tfoot th {
    background-color: map-get($primary-theme, "25");
    color: $primary-text-color;
    font-size: 0.95rem;
    text-wrap: nowrap;
    &.sort {
      cursor: pointer;
      position: sticky;
      padding-right: 1.5rem;
      &::before {
        content: "";
        position: absolute;
        height: 100%;
        width: 1.2rem;
        background-image: url(../assets/new/svgs/sort.svg);
        background-position: center;
        background-repeat: no-repeat;
        background-size: 1.2rem;
        right: 0.25rem;
        top: 0;
      }
      &.asc::before {
        background-image: url(../assets/new/svgs/sort-down.svg);
      }
      &.desc::before {
        background-image: url(../assets/new/svgs/sort-up.svg);
      }
    }
  }
  tbody tr {
    position: relative;
    cursor: pointer;
    td,
    th {
      font-weight: 500;
      font-size: 0.95rem;
      &.link {
        text-decoration: underline;
        color: $primary-theme-color;
      }
      &.actions {
        vertical-align: middle;
        padding-left: 0;
        &.action-hover button.action-icon-button:not(.show) {
          opacity: 0.15;
          transition: opacity 0.3s ease;
        }
        button.action-icon-button {
          margin: 0 1rem;
          outline: none;
          border: none;
          background-color: map-get($primary-theme, "50");
          border-radius: 4px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0.375rem;
          svg-icon.action-icon,
          svg.action-icon {
            height: 1.2rem;
            width: 1.2rem;
            display: block;
          }
          &.primary .action-icon path {
            fill: $primary-theme-color;
          }
        }
      }
    }
    td {
      transition: background-color 0.3s ease;
      font-size: 1.5 rem;
    }
    &:hover {
      td {
        background-color: #fbfbfb;
        &:first-child::before {
          content: "";
          position: absolute;
          height: 100%;
          width: 4px;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          background-color: $primary-theme-color;
          transition: height 0.3s ease;
        }
        &.action-hover.actions button.action-icon-button {
          opacity: 1;
        }
      }
    }
  }
  &.sticky-first-column {
    thead th:first-child,
    tfoot th:first-child,
    tbody td:first-child,
    tbody th:first-child {
      position: sticky;
      left: 0;
      z-index: 1;
      &::after {
        content: "";
        width: 100%;
        height: 100%;
        top: 0;
        background-color: transparent;
        display: block;
        position: absolute;
        left: 0;
        z-index: -1;
        box-shadow: 4px 0px 14px 0px #0000000f;
      }
    }
  }
  &.sticky-last-column {
    thead th:last-child,
    tfoot th:last-child,
    tbody td:last-child,
    tbody th:last-child {
      position: sticky;
      right: 0;
      z-index: 1;
      &::after {
        content: "";
        width: 100%;
        height: 100%;
        top: 0;
        background-color: transparent;
        display: block;
        position: absolute;
        left: 0;
        z-index: -1;
        box-shadow: -4px 0px 14px 0px #0000000f;
      }
    }
  }
}

.table.enc-table-1 {
  margin: 0;
  thead {
    position: sticky;
    top: 0;
    z-index: 0;
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.0588235294);
  }
  thead th,
  tfoot th,
  tbody td,
  tbody th {
    padding: 1rem 1.25rem;
    &:first-child {
      padding-left: 1.75rem;
    }
    &:last-child {
      padding-right: 1.75rem;
    }
  }
  thead th,
  tfoot th {
    background-color: $white-color;
    color: $primary-text-color;
    font-size: 1rem;
    text-wrap: nowrap;
  }
  tbody tr {
    td,
    th {
      font-weight: 500;
      font-size: 0.9375rem;
    }
  }
  &.primary {
    thead th,
    tfoot th {
      background-color: $primary-theme-color;
      color: $white-color;
    }
  }
}

.table {
  .btn-action {
    height: 2rem;
  }
  .btn-action-icon {
    height: 2rem;
    width: 2rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

.enc-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  .pagination-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-wrap: nowrap;
    font-size: 0.875rem;
  }
  pagination > ul {
    margin: 0;
    border: solid 1px $border-color;
    border-radius: 6px;
    padding: 6px;
    gap: 8px;
    li.page-item {
      .page-link {
        border: none;
        border-radius: 6px;
        font-size: 0.875rem;

        display: flex;
        align-items: center;
        justify-content: center;
      }
      &.disabled {
        opacity: 0.6;
      }
      &.pagination-first,
      &.pagination-last,
      &.pagination-prev,
      &.pagination-next {
        .page-link {
          text-indent: -9999px;
          overflow: hidden;
          &:after {
            content: "";
            display: inline-block;
            width: 0.625rem;
            height: 0.625rem;
            background-size: contain;
            background-repeat: no-repeat;
          }
        }
      }
      &.pagination-first .page-link:after {
        background-image: url(../assets/new/svgs/pagination-first.svg);
      }
      &.pagination-last .page-link:after {
        background-image: url(../assets/new/svgs/pagination-last.svg);
      }
      &.pagination-prev .page-link:after {
        background-image: url(../assets/new/svgs/pagination-prev.svg);
      }
      &.pagination-next .page-link:after {
        background-image: url(../assets/new/svgs/pagination-next.svg);
      }
    }
  }
}

accordion.enc-accordion.panel-group {
  accordion-group.panel {
    margin-bottom: 1.5rem;
    .panel.card {
      border-radius: 1rem;
      // border: 2px solid $border-color;
      border: none;
      background-color: $white-color;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
      .panel-heading {
        padding: 1rem 1.5rem;
        border-bottom: 2px solid $border-color;
        transition: border-bottom 0.2s ease-in;
        background-color: $white-color;
        .panel-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          .accordion-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            button.btn.btn-link {
              padding: 0;
              text-decoration: none;
              color: $primary-text-color;
              font-weight: 600;
              height: auto;
              font-size: 1.25rem;
              line-height: 0.5rem;
            }
            .actions button.btn {
              height: 2rem;
              line-height: 1.2;
              font-size: 1rem;
              min-width: 8rem;
              margin-right: 1.25rem;
            }
          }
          &::after {
            content: "";
            width: 2rem;
            height: 2rem;
            background-color: #f0f0f0;
            border-radius: 0.5rem;
            display: inline-block;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2232%22%20height%3D%2232%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22none%22%20stroke%3D%22currentColor%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22m6%2015l6-6l6%206%22/%3E%3C/svg%3E");
            background-position: center;
            background-size: 1.5rem;
            background-repeat: no-repeat;
            transition: transform 0.2s ease-in;
          }
        }
      }
      .panel-body.card-body {
        padding: 1rem 1.5rem;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
    &:not(.panel-open) .panel-heading {
      border-bottom-width: 0 !important;
      border-radius: 1rem;
      .panel-title::after {
        transform: rotate(180deg) !important;
      }
    }
    &.panel-open .panel-heading {
      border-radius: 1rem 1rem 0 0;
    }
  }
}

accordion.enc-accordion-1.panel-group {
  accordion-group.panel {
    margin-bottom: 1rem;
    border: none;

    .panel.card {
      border: none;
      .panel-heading {
        border-bottom: none;
        .accordion-toggle button.btn-link {
          text-decoration: none;
        }
        .panel-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          &::after {
            width: 2rem;
            height: 2rem;
            background-color: #f0f0f0;
            border-radius: 0.5rem;
            display: inline-block;
            transition: transform 0.2s ease-in;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
          }
        }
      }
      .panel-body.card-body {
        background-color: rgba-color($primary-theme-color, 0.04);
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
    &:not(.panel-open) .panel-heading {
      border-bottom-width: 0 !important;
      border-radius: 1rem;
      background-color: rgba-color($primary-theme-color, 0.04);
      .panel-title::after {
        content: "+";
        transform: rotate(180deg) !important;
      }
    }
    &.panel-open .panel-heading {
      border-radius: 1rem 1rem 0 0;
      background-color: rgba-color($primary-theme-color, 0.12);
      .panel-title::after {
        content: "-";
        transform: rotate(0deg) !important;
      }
    }
  }
}

.enc-datepicker.bs-datepicker {
  border-radius: 6px;
  border: 1px solid $border-color;
  .bs-datepicker-container {
    padding: 0;
    .bs-datepicker-head {
      background-color: transparent;
      color: $primary-text-color;
      bs-datepicker-navigation-view button {
        color: $primary-text-color;
        &.current {
          border: 1px solid $primary-text-color;
          border-radius: 6px;
        }
      }
    }
    .bs-datepicker-body {
      border-width: 0;
      padding-top: 0;
      table th,
      table td span {
        color: $primary-theme-color;
        font-weight: 600;
      }
      table tbody td span {
        border-radius: 6px;
        margin: 4px;
        height: 2.25rem;
        // line-height: 2.25rem;
        // line-height: 1;
        font-size: 1.125rem;
        &.is-other-month,
        &.is-other-month:hover {
          color: $gray-color;
          opacity: 0.7;
        }
        &.disabled {
          color: $gray-color;
        }
        &.selected {
          color: $white-color;
        }
        &:hover {
          color: $primary-theme-color;
        }
        &.today-date {
          border: 1px solid $primary-theme-color;
        }
      }
      table.days.weeks {
        thead th {
          height: 2.25rem;
          line-height: 2.25rem;
        }
        tbody td span {
          width: 2.25rem;
        }
      }
      table.years {
        tbody td span {
          line-height: 2.25rem;
        }
      }
      table.months {
        tbody td.disabled {
          color: $gray-color;
          opacity: 0.4;
        }
      }
    }
  }
}

.error-msg {
  color: $red-color;
  font-size: 15px;
  margin-top: 3px;
}

modal-container.modal {
  &:has(.enc-side-drawer) {
    .enc-side-drawer {
      &.side-drawer-right.modal-dialog {
        right: 0;
        transform: translateX(100%);
        .modal-content {
          border-radius: 30px 0 0 30px;
        }
      }

      &.side-drawer-left.modal-dialog {
        left: 0;
        transform: translateX(-100%);
        .modal-content {
          border-radius: 0 30px 30px 0;
        }
      }

      &.modal-dialog {
        position: fixed;
        margin: 0;
        height: 100%;
        top: 0;
        transition: transform 0.3s ease-in-out;
        max-width: 440px;
        width: 100%;

        .modal-content {
          height: 100%;
          border: none;
          border-radius: 0;
        }
      }
    }
  }
  &.show:has(.enc-side-drawer) .enc-side-drawer {
    &.side-drawer-left.modal-dialog,
    &.side-drawer-right.modal-dialog {
      transform: translateX(0);
    }
  }
}

.bg-box-primary {
  background-color: map-get($primary-theme, "25");
  border-radius: 12px;
}

.bg-box-secondary {
  background-color: map-get($secondary-theme, "25");
  border-radius: 12px;
}

.btn-icon {
  background: none;
  border: none;
  color: #007bff;
  font-size: 1.5rem;
  cursor: pointer;

  &:hover {
    color: #0056b3;
  }
}

.instruction-overlay {
  position: fixed;
  top: 70px;
  right: 0;
  width: 32rem;
  height: calc(100% - 70px);
  background: #fff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1050;
  overflow-y: auto;
  transition: transform 0.3s ease-in-out;
  border-radius: 16px 0 0 16px;

  .overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #ffffff;
    color: #000;
    font-size: 1.25rem;
  }

  .btn-close {
    background: #e63946;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      margin-top: 1px;
    }
  }

  .overlay-content {
    padding: 1rem;
  }
}

.page-list {
  list-style-type: none;
  padding: 0;

  li {
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .page-list-content {
    b {
      font-size: 18px;
    }

    p {
      font-size: 16px;
      color: #7c798b;
      margin: 5px;
    }
  }
}

.help-block {
  font-style: italic;
  margin-top: 1rem;
  display: block;
  color: $secondary-text-color;
  font-size: 0.875rem;
  position: relative;
  &::after {
    content: "*";
    position: absolute;
    color: $red-color;
    font-weight: 600;
    font-size: 1rem;
  }
}

.enc-sequence {
  display: flex;
  align-items: center;
  justify-content: center;
  .sequence-card {
    border-radius: 15px;
    border: 1px solid $border-color;
    flex: 1;
    .title {
      border-radius: 15px 15px 0 0;
      border: 1px solid $border-color;
      border-bottom-width: 0;
      padding: 1rem 2rem;
      font-weight: 600;
      font-size: 1.25rem;
      line-height: 1;
      background-color: $primary-theme-color;
      color: $white-color;
    }
    ul.sequence-drop-container {
      border-radius: 0 0 15px 15px;
      border: 1px solid $border-color;
      padding: 1rem 1.5rem;
      height: calc(100vh - 25.3rem);
      overflow: auto;
      margin: 0;
      list-style: none;
      position: relative;
      li.search-item {
        position: sticky;
        top: -1rem;
        margin: -1rem -1.5rem;
        padding: 1rem 1.5rem;
        width: calc(100% + 3rem);
        border-bottom: 1px solid $border-color;
        z-index: 1;
        background-color: $white-color;
      }
      &:has(li.search-item) {
        li.sequence-list-item:nth-child(2) {
          margin-top: 2rem;
        }
      }
    }
  }
  .sequence-move-actions {
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1.25rem;
    .btn-action-icon {
      height: 1.5rem;
      width: 1.5rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 1.5rem;
      border-radius: 12px;
      svg-icon svg {
        width: 1.75rem;
        height: 1.75rem;
      }
    }
  }
}

li.sequence-list-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.625rem;
  border: 1px dashed map-get($primary-theme, "200");
  background-color: map-get($primary-theme, "25");
  border-radius: 12px;
  padding: 0.875rem;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1.125rem;
  cursor: grab;
  &:last-child {
    margin-bottom: 0;
  }
  &.cdk-drag-preview,
  &.cdk-drag-placeholder,
  &:active {
    cursor: grabbing;
    border: 2px dashed map-get($primary-theme, "400");
    transform: scale(1.01);
  }
  &:has(+ .search-item) {
    margin-top: 2rem;
  }
}

.enc-d3-tooltip {
  position: absolute;
  background: rgba-color($white-color, 0.35);
  font-size: 0.75rem;
  border-radius: 10px;
  padding: 0.25rem 0.5rem;
  border: solid 1px $border-color;
}

.modal[role="dialog"] {
  --bs-modal-border-radius: 20px;
  --bs-modal-inner-border-radius: 21px;
  --bs-modal-header-padding: 1rem 1.5rem;
  --bs-modal-padding: 1rem 1.5rem;
  .modal-dialog {
    .modal-content {
      .modal-header,
      .modal-body,
      .modal-footer {
        padding: 1rem 1.5rem;
      }
    }
  }
}
.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  color: var(--bs-dropdown-link-active-bg);
  border-bottom: 1px solid #d3d3d33b;
}

.form-check-input[type="checkbox"] {
  border-color: $primary-theme-color;
}

// Insights
.insight-layout-container {
  .overview-cards {
    display: grid;
    gap: 1.125rem;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: auto auto;

    .overview-card {
      background-color: #e6e6e6;
      border-radius: 17px;
      padding: 0.75rem;
      border-radius: 17px;
      padding: 0.75rem;
      display: flex;
      align-items: self-start;
      gap: 1rem;
      position: relative;
      grid-column: span 3;

      .overview-card-icon-circle {
        border-radius: 50%;
        width: 3.95rem;
        height: 3.95rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .overview-card-icon {
          width: 1.75rem;
          height: 1.75rem;
        }
      }

      .overview-card-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        gap: 0.35rem;

        .details {
          flex: 1;
          display: flex;
          justify-content: space-between;
          // align-items: center;
          gap: 0.5rem;
          flex-direction: column;
          padding-right: 2rem;
          .title {
            font-size: 1.15rem;
            letter-spacing: -0.12px;
            font-weight: 600;
            line-height: 1.3;
            flex: 1;
          }
          .value {
            font-size: 1.5rem;
            font-weight: 800;
            letter-spacing: -0.15px;
            word-break: break-all;
            width: calc(100% + 2rem);
            // padding-right: 1rem;
            // flex-basis: 7rem;
            // text-align: right;
          }
          .desc {
            font-size: 1rem;
            font-weight: 600;
            letter-spacing: -0.1px;
            width: calc(100% + 2rem);
            // padding-right: 1rem;
          }
        }
      }

      .overview-card-info-icon {
        padding: 0;
        border: none;
        background: transparent;
        width: 1.75rem;
        height: 1.75rem;
        opacity: 0.5;
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        svg-icon svg {
          width: 1.75rem;
          height: 1.75rem;
        }
        &:hover {
          opacity: 0.8;
        }
      }

      &.purple {
        background-color: #e9e2ff;
        .overview-card-icon-circle {
          background-color: #523c8d;
        }
      }

      &.green {
        background-color: #cbeae0;
        .overview-card-icon-circle {
          background-color: #1e8d89;
        }
      }

      &.red {
        background-color: #ffd6d6;
        .overview-card-icon-circle {
          background-color: #d54848;
        }
      }

      &.yellow {
        background-color: #ffe8bc;
        .overview-card-icon-circle {
          background-color: #c98d1b;
        }
      }

      &.pink {
        background-color: #ffe5f8;
        .overview-card-icon-circle {
          background-color: #af3996;
        }
      }

      &.span-3 {
        grid-column: span 3;
      }

      &.span-4 {
        grid-column: span 4;
      }
    }

    &.staff-cards .overview-card {
      grid-column: span 4;

      &:nth-child(n + 4) {
        grid-column: span 3;
      }

      // &:nth-child(n + 4):nth-last-child(-n + 3) {
      //   grid-column: span 4;
      // }
    }
  }

  &.secondary-allocation .overview-card {
    &.trail-gap .overview-card {
      grid-column: span 4;
    }
  }

  .btn-filter {
    background-color: #333333;
    svg-icon svg {
      width: 1.65rem;
      height: 1.65rem;
    }
  }

  .chart-cards-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;

    .enc-card {
      aspect-ratio: 6 / 4.85;
      display: flex;
      flex-direction: column;

      &.full-span {
        aspect-ratio: 12.25 / 4.85;
        grid-column: span 2;
      }

      .card-content {
        flex: 1;
        &::-webkit-scrollbar-track {
          background: $white-color;
        }
      }
    }

    .enc-card .card-content {
      // min-height: 32rem;
      // height: 34rem;
      overflow: auto;

      &.chart-loading-placeholder {
        .chart-loading-placeholder-image {
          display: none;
        }
        &.show-chart-loading-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          .chart-loading-placeholder-image {
            display: block;
          }
        }
      }
    }
  }

  .card-header.insight-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-top: 0;
    padding-bottom: 0;
    height: 4.25rem;
    .btn.icon-btn {
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-color: #d8d3e8;

      .icon-btn-svg {
        width: 1.8rem;
        height: 1.8rem;
      }

      &:disabled {
        opacity: 0.4;
      }
    }
    .form-select,
    .form-control {
      height: 2.5rem;
      min-width: 4.75rem;
    }
    .btn-group > .btn {
      border-color: #d8d3e8;
      height: 2.5rem;
    }
    .level-shift-button .btn-group > .btn.icon-btn {
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-color: #d8d3e8;
      svg {
        width: 1.8rem;
        height: 1.8rem;
      }
    }
  }

  .insight-sticky-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .sticky-insight-tab.btn-group {
      border-bottom: 3px solid var(--bs-secondary);
      gap: 0.25rem;
      border-radius: 0;
      .btn {
        border-radius: 20px 20px 0px 0px;
        font-size: 1rem;
        padding: 1rem 3rem;
        background-color: rgba-color($gray-color, 0.2);
        font-weight: 600;
        color: $black-color;
        height: auto;
        &.active {
          color: var(--bs-black);
          background-color: var(--bs-secondary);
          border-color: transparent;
        }
      }
    }
  }
}
