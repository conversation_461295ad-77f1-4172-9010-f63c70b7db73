import {
  Directive,
  ElementRef,
  EventEmitter,
  HostBinding,
  inject,
  Input,
  OnChanges,
  Output,
  Renderer2,
  SimpleChang<PERSON>,
} from "@angular/core";

@Directive({
  selector: "[appTemplateVarConfig]",
  exportAs: "appTemplateVarConfig",
  standalone: true,
})
export class TemplateVarConfigDirective implements OnChanges {
  @Input() template: string = "";
  @Output() selectVar: EventEmitter<{ index: number; value: string }> =
    new EventEmitter();
  @Output() change: EventEmitter<string> = new EventEmitter();;

  element = inject(ElementRef);
  renderer = inject(Renderer2);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["template"]) {
      this.replaceTags();
    }
  }

  replaceTags() {
    const content = (this.template || "")
      ?.replace(/<<(.+?)>>/g, (match, innerText) => {
        return `<span ${
          innerText !== "Var" ? 'class="assigned"' : ""
        }>${innerText}</span>`;
      })
      ?.replace(/\n/g, "<br>");

    this.element.nativeElement.innerHTML = content;

    this.element.nativeElement
      .querySelectorAll("span")
      .forEach((span: HTMLElement, index: number) => {
        this.renderer.listen(span, "click", (event) => {
          const value = span?.innerHTML;
          this.selectVar.emit({ index, value: value === "Var" ? null : value });
        });
      });
  }

  onUpdateVariable({ index, value }: { index: number; value: string }) {
    let matchIndex = -1;
    const newTemplate = this.template.replace(/<<(.+?)>>/g, (match, p1, offset) => {
      matchIndex++;
      if (matchIndex === index) {
        return `<<${value}>>`;
      }
      return match;
    });

    this.change.emit(newTemplate);
  }

  @HostBinding("class") get hostClass() {
    return "form-control template-var-config";
  }
}
